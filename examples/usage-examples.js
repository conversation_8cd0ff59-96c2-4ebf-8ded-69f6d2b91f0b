import { CustomFieldsWorkflow } from '../src/main.js';
import { CustomFieldsManager, Logger } from '../src/customFieldsManager.js';

/**
 * Example usage scenarios for the Custom Fields Management Workflow
 */

async function example1_BackupOnly() {
  Logger.info('=== Example 1: Backup All Custom Fields ===');
  
  const workflow = new CustomFieldsWorkflow();
  
  try {
    const result = await workflow.backupOnly('./examples/backups');
    Logger.success(`Backup completed! Retrieved ${result.fieldsRetrieved} fields`);
    Logger.info(`Backup saved to: ${result.backupPath}`);
  } catch (error) {
    Logger.error('Backup failed', error);
  }
}

async function example2_ListAndAnalyze() {
  Logger.info('=== Example 2: List and Analyze Custom Fields ===');
  
  const manager = new CustomFieldsManager();
  
  try {
    // Retrieve all fields
    const allFields = await manager.getAllCustomFields();
    
    // Get summary
    const summary = manager.getFieldsSummary();
    
    // Analyze field types
    const fieldTypes = {};
    allFields.forEach(field => {
      const type = field.type || 'unknown';
      fieldTypes[type] = (fieldTypes[type] || 0) + 1;
    });
    
    Logger.info('Field Type Distribution:');
    Object.entries(fieldTypes).forEach(([type, count]) => {
      Logger.info(`  ${type}: ${count} fields`);
    });
    
    // Find required fields
    const requiredFields = allFields.filter(field => field.required);
    Logger.info(`Required fields: ${requiredFields.length}`);
    
    // Create backup with analysis
    const backupPath = await manager.createBackup(allFields, './examples/backups');
    Logger.success(`Analysis complete! Backup saved to: ${backupPath}`);
    
  } catch (error) {
    Logger.error('Analysis failed', error);
  }
}

async function example3_DryRunDeletion() {
  Logger.info('=== Example 3: Dry Run Deletion ===');
  
  const workflow = new CustomFieldsWorkflow();
  
  try {
    // First, get all fields to find some IDs for testing
    const manager = new CustomFieldsManager();
    const allFields = await manager.getAllCustomFields();
    
    if (allFields.length === 0) {
      Logger.warn('No fields found for dry run example');
      return;
    }
    
    // Take first 2 field IDs for dry run (you can modify this)
    const testFieldIds = allFields.slice(0, 2).map(field => field.id);
    
    Logger.info(`Performing dry run deletion for fields: ${testFieldIds.join(', ')}`);
    
    const result = await workflow.dryRunDeletion(testFieldIds);
    
    if (result.deletionResults) {
      Logger.info('Dry run results:');
      Logger.info(`  Would delete: ${result.deletionResults.total} fields`);
      Logger.info(`  Simulated successful: ${result.deletionResults.successful.length}`);
      Logger.info(`  Simulated failed: ${result.deletionResults.failed.length}`);
    }
    
  } catch (error) {
    Logger.error('Dry run failed', error);
  }
}

async function example4_ConditionalDeletion() {
  Logger.info('=== Example 4: Conditional Deletion (Example Only) ===');
  
  const manager = new CustomFieldsManager();
  
  try {
    // Retrieve all fields
    const allFields = await manager.getAllCustomFields();
    
    // Example: Find fields that match certain criteria
    // (This is just an example - modify criteria as needed)
    const fieldsToDelete = allFields.filter(field => {
      // Example criteria: fields with specific naming pattern
      const name = field.name || field.label || '';
      return name.toLowerCase().includes('test') || name.toLowerCase().includes('temp');
    });
    
    if (fieldsToDelete.length === 0) {
      Logger.info('No fields match deletion criteria');
      return;
    }
    
    Logger.info(`Found ${fieldsToDelete.length} fields matching deletion criteria:`);
    fieldsToDelete.forEach(field => {
      Logger.info(`  - ${field.name || field.label} (ID: ${field.id})`);
    });
    
    // Create backup first
    const backupPath = await manager.createBackup(allFields, './examples/backups');
    Logger.success(`Backup created: ${backupPath}`);
    
    // Perform dry run
    const fieldIds = fieldsToDelete.map(field => field.id);
    Logger.info('Performing dry run for conditional deletion...');
    
    const dryRunResult = await manager.deleteMultipleCustomFields(fieldIds, {
      dryRun: true,
      batchSize: 3,
      delayBetweenBatches: 500
    });
    
    Logger.info('Conditional deletion dry run completed');
    Logger.info(`Would delete ${dryRunResult.successful.length} fields`);
    
    // Note: To actually delete, set dryRun: false
    // const actualResult = await manager.deleteMultipleCustomFields(fieldIds, {
    //   dryRun: false,
    //   batchSize: 3,
    //   delayBetweenBatches: 1000
    // });
    
  } catch (error) {
    Logger.error('Conditional deletion example failed', error);
  }
}

async function example5_FullWorkflow() {
  Logger.info('=== Example 5: Full Workflow (Backup + Deletion) ===');
  Logger.warn('⚠️  This example includes actual deletion - use with caution!');
  
  const workflow = new CustomFieldsWorkflow();
  
  try {
    // Example field IDs to delete (replace with actual IDs you want to delete)
    const fieldsToDelete = [
      // 'field-id-1',
      // 'field-id-2'
    ];
    
    if (fieldsToDelete.length === 0) {
      Logger.info('No field IDs specified for deletion - skipping actual deletion');
      Logger.info('To use this example, add actual field IDs to the fieldsToDelete array');
      return;
    }
    
    const result = await workflow.executeWorkflow({
      createBackup: true,
      fieldsToDelete: fieldsToDelete,
      dryRun: false, // Set to true for dry run
      backupDir: './examples/backups'
    });
    
    Logger.success('Full workflow completed!');
    Logger.info(`Fields retrieved: ${result.fieldsRetrieved}`);
    Logger.info(`Backup created: ${result.backupCreated}`);
    
    if (result.deletionResults) {
      Logger.info(`Deletion results: ${result.deletionResults.successful.length} successful, ${result.deletionResults.failed.length} failed`);
    }
    
  } catch (error) {
    Logger.error('Full workflow failed', error);
  }
}

// Main function to run all examples
async function runAllExamples() {
  Logger.info('🚀 Starting Custom Fields Management Examples');
  
  try {
    await example1_BackupOnly();
    console.log('\n' + '='.repeat(60) + '\n');
    
    await example2_ListAndAnalyze();
    console.log('\n' + '='.repeat(60) + '\n');
    
    await example3_DryRunDeletion();
    console.log('\n' + '='.repeat(60) + '\n');
    
    await example4_ConditionalDeletion();
    console.log('\n' + '='.repeat(60) + '\n');
    
    await example5_FullWorkflow();
    
    Logger.success('✅ All examples completed!');
    
  } catch (error) {
    Logger.error('Examples execution failed', error);
  }
}

// Run examples if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllExamples();
}

export {
  example1_BackupOnly,
  example2_ListAndAnalyze,
  example3_DryRunDeletion,
  example4_ConditionalDeletion,
  example5_FullWorkflow,
  runAllExamples
};
