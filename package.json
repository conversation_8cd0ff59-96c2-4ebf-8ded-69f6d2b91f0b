{"name": "cf-delete", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "cf:interactive": "clear && node src/cli.js interactive", "cf:backup": "node src/cli.js backup", "cf:list": "node src/cli.js list", "cf:delete": "node src/cli.js delete", "cf:dry-run": "node src/cli.js dry-run", "cf:help": "node src/cli.js help", "cf:workflow": "node src/main.js", "test": "node test/basic-test.js", "examples": "node examples/usage-examples.js"}, "devDependencies": {"vite": "^6.3.5"}, "dependencies": {"fs-extra": "^11.3.0", "inquirer": "^12.6.3"}}