# Quick Start Guide

Get up and running with the Custom Fields Management Workflow in minutes!

## 🚀 Quick Setup

1. **Install dependencies:**
   ```bash
   npm install
   # or
   pnpm install
   ```

2. **Update API configuration** in `src/customFieldsManager.js`:
   - Replace `locationId` with your actual location ID
   - Replace `parentId` with your actual parent ID  
   - Update authorization tokens if needed

3. **Test the setup:**
   ```bash
   npm run test
   ```

## 🎯 Common Use Cases

### 1. Create a Backup of All Custom Fields
```bash
# Using npm scripts
npm run cf:backup

# Or directly
node src/cli.js backup
```

### 2. List All Custom Fields
```bash
npm run cf:list
```

### 3. Test Deletion (Dry Run)
```bash
# Replace field1,field2 with actual field IDs
node src/cli.js dry-run field1,field2,field3
```

### 4. Delete Specific Fields (with backup)
```bash
# ⚠️ This will actually delete fields!
node src/cli.js delete field1,field2 --backup-dir ./my-backups
```

## 📋 Step-by-Step Workflow

### Safe Deletion Process:

1. **First, create a backup:**
   ```bash
   npm run cf:backup
   ```

2. **List fields to identify what to delete:**
   ```bash
   npm run cf:list
   ```

3. **Test deletion with dry run:**
   ```bash
   node src/cli.js dry-run FIELD_ID_1,FIELD_ID_2
   ```

4. **If dry run looks good, perform actual deletion:**
   ```bash
   node src/cli.js delete FIELD_ID_1,FIELD_ID_2
   ```

## 🔧 Configuration

### API Settings
Edit `src/customFieldsManager.js` and update:

```javascript
const API_CONFIG = {
  baseUrl: 'https://backend.leadconnectorhq.com',
  locationId: 'YOUR_LOCATION_ID',        // ← Update this
  parentId: 'YOUR_PARENT_ID',            // ← Update this
  headers: {
    'authorization': 'Bearer YOUR_TOKEN', // ← Update if needed
    // ... other headers
  }
};
```

### Finding Your IDs

You can find these values by:
1. Looking at the existing `src/get.js` file
2. Checking your browser's network tab when using the web interface
3. Consulting your API documentation

## 🛡️ Safety Features

- ✅ **Automatic backups** before any deletion
- ✅ **Dry run mode** to test operations
- ✅ **Batch processing** with delays
- ✅ **Comprehensive logging**
- ✅ **Error handling and recovery**

## 📁 File Structure After Running

```
your-project/
├── backups/                          # Created automatically
│   └── custom-fields-backup-*.json   # Timestamped backups
├── src/
│   ├── customFieldsManager.js        # Core functionality
│   ├── main.js                       # Workflow orchestrator
│   └── cli.js                        # Command line interface
└── examples/
    └── backups/                      # Example backups
```

## 🧪 Testing

Run the included tests to verify everything works:

```bash
npm run test
```

Run examples to see the system in action:

```bash
npm run examples
```

## ⚠️ Important Notes

1. **Always backup first** - Deletion is permanent!
2. **Test with dry run** - Verify field IDs before actual deletion
3. **Update API tokens** - Ensure your tokens are current and valid
4. **Check rate limits** - The system includes delays, but monitor API usage

## 🆘 Troubleshooting

### Common Issues:

**"No custom fields found"**
- Check your `locationId` and `parentId` values
- Verify API tokens are valid

**"Authentication failed"**
- Update the authorization token in `API_CONFIG`
- Check if tokens have expired

**"Backup directory errors"**
- Ensure the backup directory is writable
- Check disk space

### Getting Help:

1. Check the logs for detailed error messages
2. Run with dry-run mode first
3. Verify API configuration
4. Review the full README.md for detailed documentation

## 🎉 You're Ready!

Start with a simple backup to test your setup:

```bash
npm run cf:backup
```

If that works, you're all set to manage your custom fields safely! 🚀
