# Quick Start Guide

Get up and running with the Custom Fields Management Workflow in minutes!

## 🚀 Quick Setup

1. **Install dependencies:**

   ```bash
   npm install
   # or
   pnpm install
   ```

2. **Update API configuration** in `src/customFieldsManager.js`:

   - Replace `locationId` with your actual location ID
   - Replace `parentId` with your actual parent ID
   - Update authorization tokens if needed

3. **Test the setup:**
   ```bash
   npm run test
   ```

## 🎯 Common Use Cases

### 1. Start Interactive Mode (Recommended for All Users)

```bash
# Start the enhanced interactive CLI
npm run cf:interactive
# or
node src/cli.js interactive
# or simply
node src/cli.js
```

### 2. Quick Backup with Interactive Configuration

```bash
npm run cf:backup
# This will:
# - Ask for pagination settings (max pages, page size)
# - Show time/record estimates
# - Let you choose backup directory
# - Create timestamped backup
```

### 3. Interactive Field Browsing and Management

```bash
npm run cf:list
# This will:
# - Configure pagination interactively
# - Display fields in beautiful tables
# - Allow searching and filtering
# - Enable pagination through results
```

### 4. Safe Interactive Deletion

```bash
npm run cf:delete
# This will:
# - Configure pagination settings
# - Create mandatory backup first
# - Show all fields in table format
# - Let you select fields by:
#   • Numbers (1,3,5-10)
#   • Name patterns (test*, *temp*)
#   • Field types
#   • Interactive browsing
# - Show detailed confirmation
# - Execute deletion with progress tracking
```

### 5. Test Deletion (Dry Run)

```bash
npm run cf:dry-run
# Same as deletion but simulates the process
# Perfect for testing your selection criteria
```

## 📋 Step-by-Step Interactive Workflow

### Enhanced Safe Deletion Process:

1. **Start Interactive Mode:**

   ```bash
   npm run cf:interactive
   ```

2. **Choose "Delete custom fields" from the menu**

   - The system will guide you through each step
   - No need to know field IDs beforehand!

3. **Configure Pagination:**

   - Set maximum pages to retrieve (default: 10)
   - Set page size (default: 1000)
   - Review time estimates

4. **Automatic Backup Creation:**

   - System creates mandatory backup
   - Choose backup directory (timestamped by default)
   - Backup confirmation displayed

5. **Interactive Field Selection:**

   - View fields in beautiful table format
   - Choose selection method:
     - **Numbers**: "1,3,5-10"
     - **Patterns**: "test*", "*temp\*"
     - **Types**: Select by field type
     - **Browse**: Page through with checkboxes

6. **Review and Confirm:**

   - See exactly what will be deleted
   - View backup location
   - Final confirmation prompt

7. **Execution with Progress:**
   - Real-time progress tracking
   - Batch processing with delays
   - Detailed success/failure reporting

### Alternative: Quick Commands for Advanced Users

If you prefer direct commands:

```bash
# Quick backup
npm run cf:backup

# Browse fields
npm run cf:list

# Test deletion (dry run)
npm run cf:dry-run

# Actual deletion
npm run cf:delete
```

## 🔧 Configuration

### API Settings

Edit `src/customFieldsManager.js` and update:

```javascript
const API_CONFIG = {
  baseUrl: "https://backend.leadconnectorhq.com",
  locationId: "YOUR_LOCATION_ID", // ← Update this
  parentId: "YOUR_PARENT_ID", // ← Update this
  headers: {
    authorization: "Bearer YOUR_TOKEN", // ← Update if needed
    // ... other headers
  },
};
```

### Finding Your IDs

You can find these values by:

1. Looking at the existing `src/get.js` file
2. Checking your browser's network tab when using the web interface
3. Consulting your API documentation

## 🛡️ Safety Features

- ✅ **Automatic backups** before any deletion
- ✅ **Dry run mode** to test operations
- ✅ **Batch processing** with delays
- ✅ **Comprehensive logging**
- ✅ **Error handling and recovery**

## 📁 File Structure After Running

```
your-project/
├── backups/                          # Created automatically
│   └── custom-fields-backup-*.json   # Timestamped backups
├── src/
│   ├── customFieldsManager.js        # Core functionality
│   ├── main.js                       # Workflow orchestrator
│   └── cli.js                        # Command line interface
└── examples/
    └── backups/                      # Example backups
```

## 🧪 Testing

Run the included tests to verify everything works:

```bash
npm run test
```

Run examples to see the system in action:

```bash
npm run examples
```

## ⚠️ Important Notes

1. **Always backup first** - Deletion is permanent!
2. **Test with dry run** - Verify field IDs before actual deletion
3. **Update API tokens** - Ensure your tokens are current and valid
4. **Check rate limits** - The system includes delays, but monitor API usage

## 🆘 Troubleshooting

### Common Issues:

**"No custom fields found"**

- Check your `locationId` and `parentId` values
- Verify API tokens are valid

**"Authentication failed"**

- Update the authorization token in `API_CONFIG`
- Check if tokens have expired

**"Backup directory errors"**

- Ensure the backup directory is writable
- Check disk space

### Getting Help:

1. Check the logs for detailed error messages
2. Run with dry-run mode first
3. Verify API configuration
4. Review the full README.md for detailed documentation

## 🎉 You're Ready!

Start with a simple backup to test your setup:

```bash
npm run cf:backup
```

If that works, you're all set to manage your custom fields safely! 🚀
