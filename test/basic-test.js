import { CustomFieldsManager, Logger } from '../src/customFieldsManager.js';
import { CustomFieldsWorkflow } from '../src/main.js';
import fs from 'fs-extra';

/**
 * Basic tests for the Custom Fields Management system
 */

class BasicTests {
  constructor() {
    this.testResults = {
      passed: 0,
      failed: 0,
      total: 0
    };
  }

  /**
   * Test helper to run individual tests
   */
  async runTest(testName, testFunction) {
    this.testResults.total++;
    
    try {
      Logger.info(`🧪 Running test: ${testName}`);
      await testFunction();
      this.testResults.passed++;
      Logger.success(`✅ Test passed: ${testName}`);
    } catch (error) {
      this.testResults.failed++;
      Logger.error(`❌ Test failed: ${testName}`, error);
    }
  }

  /**
   * Test 1: CustomFieldsManager instantiation
   */
  async test_ManagerInstantiation() {
    const manager = new CustomFieldsManager();
    
    if (!manager) {
      throw new Error('Failed to instantiate CustomFieldsManager');
    }
    
    if (manager.allCustomFields.length !== 0) {
      throw new Error('Initial allCustomFields should be empty');
    }
    
    if (manager.totalRetrieved !== 0) {
      throw new Error('Initial totalRetrieved should be 0');
    }
  }

  /**
   * Test 2: CustomFieldsWorkflow instantiation
   */
  async test_WorkflowInstantiation() {
    const workflow = new CustomFieldsWorkflow();
    
    if (!workflow) {
      throw new Error('Failed to instantiate CustomFieldsWorkflow');
    }
    
    if (!workflow.manager) {
      throw new Error('Workflow should have a manager instance');
    }
    
    if (workflow.backupPath !== null) {
      throw new Error('Initial backupPath should be null');
    }
  }

  /**
   * Test 3: API Configuration validation
   */
  async test_APIConfiguration() {
    const { API_CONFIG } = await import('../src/customFieldsManager.js');
    
    if (!API_CONFIG.baseUrl) {
      throw new Error('API_CONFIG.baseUrl is required');
    }
    
    if (!API_CONFIG.locationId) {
      throw new Error('API_CONFIG.locationId is required');
    }
    
    if (!API_CONFIG.headers.authorization) {
      throw new Error('API_CONFIG.headers.authorization is required');
    }
    
    if (!API_CONFIG.headers.authorization.startsWith('Bearer ')) {
      throw new Error('Authorization header should start with "Bearer "');
    }
  }

  /**
   * Test 4: Backup directory creation
   */
  async test_BackupDirectoryCreation() {
    const testDir = './test/temp-backup';
    const manager = new CustomFieldsManager();
    
    // Ensure directory doesn't exist
    await fs.remove(testDir);
    
    try {
      // This should create the directory
      await manager.createBackup([], testDir);
      throw new Error('Should have thrown error for empty fields array');
    } catch (error) {
      if (!error.message.includes('No custom fields data to backup')) {
        throw new Error('Should throw specific error for empty fields');
      }
    }
    
    // Clean up
    await fs.remove(testDir);
  }

  /**
   * Test 5: Field summary generation
   */
  async test_FieldsSummary() {
    const manager = new CustomFieldsManager();
    
    // Test with empty fields
    let summary = manager.getFieldsSummary();
    if (summary.total !== 0) {
      throw new Error('Empty fields summary should have total 0');
    }
    
    // Test with mock fields
    manager.allCustomFields = [
      { id: 'test1', name: 'Test Field 1', type: 'text', required: true },
      { id: 'test2', name: 'Test Field 2', type: 'number', required: false }
    ];
    
    summary = manager.getFieldsSummary();
    if (summary.total !== 2) {
      throw new Error('Summary should show 2 fields');
    }
    
    if (summary.fields.length !== 2) {
      throw new Error('Summary fields array should have 2 items');
    }
    
    if (summary.fields[0].id !== 'test1') {
      throw new Error('First field ID should be test1');
    }
  }

  /**
   * Test 6: Dry run deletion simulation
   */
  async test_DryRunDeletion() {
    const manager = new CustomFieldsManager();
    
    const result = await manager.deleteCustomField('test-field-id', true);
    
    if (!result.success) {
      throw new Error('Dry run should return success');
    }
    
    if (!result.dryRun) {
      throw new Error('Result should indicate dry run');
    }
    
    if (result.fieldId !== 'test-field-id') {
      throw new Error('Result should include field ID');
    }
  }

  /**
   * Test 7: Batch deletion options validation
   */
  async test_BatchDeletionValidation() {
    const manager = new CustomFieldsManager();
    
    try {
      await manager.deleteMultipleCustomFields([]);
      throw new Error('Should throw error for empty field IDs array');
    } catch (error) {
      if (!error.message.includes('must not be empty')) {
        throw new Error('Should throw specific error for empty array');
      }
    }
    
    try {
      await manager.deleteMultipleCustomFields(null);
      throw new Error('Should throw error for null field IDs');
    } catch (error) {
      if (!error.message.includes('Field IDs array is required')) {
        throw new Error('Should throw specific error for null input');
      }
    }
  }

  /**
   * Test 8: Logger functionality
   */
  async test_LoggerFunctionality() {
    // Test that logger methods don't throw errors
    Logger.info('Test info message');
    Logger.warn('Test warning message');
    Logger.error('Test error message');
    Logger.success('Test success message');
    
    // Test with data
    Logger.info('Test with data', { test: 'data' });
    Logger.error('Test error with error object', new Error('Test error'));
  }

  /**
   * Test 9: Workflow backup-only functionality
   */
  async test_WorkflowBackupOnly() {
    const workflow = new CustomFieldsWorkflow();
    
    // Mock the manager's getAllCustomFields method to avoid actual API call
    workflow.manager.getAllCustomFields = async () => {
      return [
        { id: 'mock1', name: 'Mock Field 1', type: 'text' },
        { id: 'mock2', name: 'Mock Field 2', type: 'number' }
      ];
    };
    
    // Mock the createBackup method to avoid file operations
    workflow.manager.createBackup = async (fields, dir) => {
      return `${dir}/mock-backup.json`;
    };
    
    const result = await workflow.backupOnly('./test/mock-backup');
    
    if (!result.success) {
      throw new Error('Backup-only workflow should succeed');
    }
    
    if (result.fieldsRetrieved !== 2) {
      throw new Error('Should report 2 fields retrieved');
    }
    
    if (!result.backupCreated) {
      throw new Error('Should indicate backup was created');
    }
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    Logger.info('🧪 Starting Basic Tests for Custom Fields Management');
    
    await this.runTest('Manager Instantiation', () => this.test_ManagerInstantiation());
    await this.runTest('Workflow Instantiation', () => this.test_WorkflowInstantiation());
    await this.runTest('API Configuration', () => this.test_APIConfiguration());
    await this.runTest('Backup Directory Creation', () => this.test_BackupDirectoryCreation());
    await this.runTest('Fields Summary', () => this.test_FieldsSummary());
    await this.runTest('Dry Run Deletion', () => this.test_DryRunDeletion());
    await this.runTest('Batch Deletion Validation', () => this.test_BatchDeletionValidation());
    await this.runTest('Logger Functionality', () => this.test_LoggerFunctionality());
    await this.runTest('Workflow Backup Only', () => this.test_WorkflowBackupOnly());
    
    // Display results
    console.log('\n' + '='.repeat(60));
    Logger.info('🧪 Test Results Summary:');
    Logger.success(`✅ Passed: ${this.testResults.passed}`);
    
    if (this.testResults.failed > 0) {
      Logger.error(`❌ Failed: ${this.testResults.failed}`);
    }
    
    Logger.info(`📊 Total: ${this.testResults.total}`);
    
    const successRate = ((this.testResults.passed / this.testResults.total) * 100).toFixed(1);
    Logger.info(`📈 Success Rate: ${successRate}%`);
    
    if (this.testResults.failed === 0) {
      Logger.success('🎉 All tests passed!');
    } else {
      Logger.warn('⚠️  Some tests failed. Please review the errors above.');
    }
    
    return this.testResults;
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const tests = new BasicTests();
  tests.runAllTests().catch(error => {
    Logger.error('Test execution failed', error);
    process.exit(1);
  });
}

export { BasicTests };
