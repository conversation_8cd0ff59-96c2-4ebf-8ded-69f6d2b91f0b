# Custom Fields Management Workflow

A comprehensive Node.js application for managing custom fields with pagination support, automatic backup creation, and safe deletion functionality.

## Features

- ✅ **Complete Pagination Support**: Retrieves ALL custom fields across multiple pages
- 💾 **Automatic Backup Creation**: Creates timestamped JSON backups before any operations
- 🛡️ **Safe Deletion Process**: Includes dry-run capabilities and confirmation prompts
- 📊 **Comprehensive Logging**: Detailed logging for all operations
- 🔄 **Batch Processing**: Handles large-scale operations efficiently
- 🎯 **Error Handling**: Robust error handling and recovery mechanisms

## Installation

1. Clone or download this repository
2. Install dependencies:
   ```bash
   npm install
   # or
   pnpm install
   ```

## Project Structure

```
src/
├── customFieldsManager.js  # Core functionality for field management
├── main.js                 # Main workflow orchestrator
├── cli.js                  # Command-line interface
├── get.js                  # Original GET implementation (reference)
└── delete.js               # Original DELETE implementation (reference)
```

## Usage

### 1. Enhanced Interactive CLI (Recommended)

The enhanced CLI provides a user-friendly, guided experience:

```bash
# Start interactive mode (recommended for all users)
node src/cli.js interactive
# or simply
node src/cli.js

# Quick commands for advanced users
node src/cli.js backup         # Interactive backup with pagination config
node src/cli.js list           # Interactive field listing and browsing
node src/cli.js delete         # Interactive deletion with field selection
node src/cli.js dry-run        # Interactive dry-run testing
node src/cli.js help           # Show help information
```

### Enhanced Features:

#### 🎯 **Interactive Pagination Configuration**

- Configure maximum pages to retrieve (default: 10)
- Set custom page sizes (default: 1000)
- View time and record estimations before starting
- Progress tracking during retrieval

#### 🛡️ **Mandatory Safety Backups**

- Automatic backup creation before ALL operations
- Intelligent timestamped backup directories
- No option to skip backups (safety first!)
- Clear backup confirmation and file paths

#### 🎨 **Smart Field Selection**

- **By Numbers**: Select fields using numbers or ranges (e.g., "1,3,5-10")
- **By Pattern**: Use wildcards to match field names (e.g., "test*", "*temp\*")
- **By Type**: Select all fields of specific types
- **Interactive Browsing**: Page through fields with checkboxes
- **Search Functionality**: Find fields by name, type, or ID

#### 📊 **Enhanced Display**

- Beautiful table format for field listings
- Pagination support for large field lists
- Real-time progress indicators
- Detailed confirmation prompts

### 2. Programmatic Usage

You can also use the classes directly in your code:

```javascript
import { CustomFieldsWorkflow } from "./src/main.js";
import { CustomFieldsManager } from "./src/customFieldsManager.js";

// Create workflow instance
const workflow = new CustomFieldsWorkflow();

// Example 1: Backup only
await workflow.backupOnly("./backups");

// Example 2: Dry run deletion
await workflow.dryRunDeletion(["field-id-1", "field-id-2"]);

// Example 3: Full workflow with deletion
await workflow.executeWorkflow({
  createBackup: true,
  fieldsToDelete: ["field-id-to-delete"],
  dryRun: false,
  backupDir: "./backups",
});

// Example 4: Using CustomFieldsManager directly
const manager = new CustomFieldsManager();
const allFields = await manager.getAllCustomFields();
const backupPath = await manager.createBackup(allFields);
```

## Configuration

The API configuration is located in `src/customFieldsManager.js`. Update the following values as needed:

```javascript
const API_CONFIG = {
  baseUrl: "https://backend.leadconnectorhq.com",
  locationId: "your-location-id",
  parentId: "your-parent-id",
  headers: {
    authorization: "Bearer your-token",
    // ... other headers
  },
};
```

## Backup Format

Backups are saved as JSON files with the following structure:

```json
{
  "metadata": {
    "timestamp": "2024-01-15T10:30:00.000Z",
    "totalFields": 150,
    "backupVersion": "1.0",
    "locationId": "your-location-id",
    "parentId": "your-parent-id"
  },
  "customFields": [
    {
      "id": "field-id-1",
      "name": "Field Name",
      "type": "text",
      "required": false
      // ... other field properties
    }
    // ... more fields
  ]
}
```

## Safety Features

### 1. Automatic Backups

- Backups are created automatically before any deletion operations
- Timestamped filenames prevent overwrites
- Includes metadata for easy restoration

### 2. Dry Run Mode

- Test deletion operations without actually deleting
- Shows exactly what would be deleted
- Helps verify field IDs before actual deletion

### 3. Batch Processing

- Processes deletions in configurable batches
- Includes delays between batches to respect API limits
- Continues processing even if individual deletions fail

### 4. Comprehensive Logging

- Detailed logs for all operations
- Error tracking and reporting
- Success/failure summaries

## API Endpoints

The application uses the following endpoints:

- **GET**: `/locations/{locationId}/customFields/search` - Retrieve custom fields with pagination
- **DELETE**: `/locations/{locationId}/customFields/{fieldId}` - Delete individual custom field

## Error Handling

The application includes comprehensive error handling:

- Network errors and timeouts
- Invalid JSON responses
- Authentication failures
- Individual deletion failures
- File system errors

## Development

### Running Tests

```bash
# Run the main workflow (backup only by default)
node src/main.js

# Run CLI with different commands
node src/cli.js backup
node src/cli.js list
```

### Adding New Features

1. Core functionality goes in `customFieldsManager.js`
2. Workflow orchestration in `main.js`
3. CLI commands in `cli.js`

## Troubleshooting

### Common Issues

1. **Authentication Errors**: Update the authorization tokens in `API_CONFIG`
2. **No Fields Retrieved**: Check the `locationId` and `parentId` values
3. **Backup Directory Errors**: Ensure the backup directory is writable
4. **Rate Limiting**: Increase delays between batch operations

### Debug Mode

Enable detailed logging by modifying the Logger class or adding console.log statements as needed.

## Security Considerations

- ⚠️ **API Tokens**: Keep your API tokens secure and rotate them regularly
- ⚠️ **Backup Files**: Backup files contain sensitive data - store them securely
- ⚠️ **Deletion Operations**: Always test with dry-run before actual deletion

## License

This project is for internal use. Please ensure compliance with your organization's policies and the API provider's terms of service.
