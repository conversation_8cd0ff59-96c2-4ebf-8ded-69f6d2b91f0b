const myHeaders = new Headers();
myHeaders.append(
  "authorization",
  "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzb3VyY2UiOiJXRUJfVVNFUiIsImNoYW5uZWwiOiJBUFAiLCJzb3VyY2VJZCI6ImdNN09na0lUZ2ZmQVIxNXRVN2tWIiwic291cmNlTmFtZSI6IkFsLUFtaW4gSXNsYW1OZXJvYiAiLCJjb21wYW55SWQiOiJpRTd1NGJNcmhDcG4xVWl1Y0lqWCIsIm1ldGEiOnsidXNlclJvbGUiOiJhZG1pbiIsInVzZXJUeXBlIjoiYWdlbmN5In0sInByaW1hcnlVc2VyIjp7fSwiaWF0IjoxNzQ5MjE5Nzg1LCJleHAiOjE3NDkyMjMzODUsImp0aSI6IjY3ZjVjMWJjMDU4OGVlMTdhZDFlMGM0NiJ9.SQ6CF1Oocjb69V7TuKBCW-dJqJALWomDkFOicShbJC4"
);
myHeaders.append("channel", "APP");
myHeaders.append("source", "WEB_USER");
myHeaders.append(
  "token-id",
  "eyJhbGciOiJSUzI1NiIsImtpZCI6IjZlODk1YzQ3YTA0YzVmNmRlMzExMmFmZjE2ODFhMzUwNzdkMWNjZDQiLCJ0eXAiOiJKV1QifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Dx6LxPNuzo0-dEKgIrf6oN2lnWbDeVQXgVdb9I0c_O1y-TY2OBlzgV36f62n0VSaRmWFuJn5wpmgb-IC6mBg-2T7IZGlGK442EXmK6CVTO1ZgI_GzMvobE53pBJPC9rZCoeDWjCQMC9Dibjkfx9pX33mNsoco8cbSuvlCUnO2QmVuTCm7E9ySWLSfLxPjtM-2t7Hti2XujgNORTJSnsrmPGS2zK5fryVMHL8QnAD7vfjZTt8xdFrtwJGapQkT4lvdCe1szNv8TPkXW6r64EkfIdzBrbgLM_bc9jkDEbDmWT6Vx-ulyGt3otHDWUke4XAVXTZuXlJWgCCQYpsi6GPSQ"
);
myHeaders.append("version", "2021-07-28");

const requestOptions = {
  method: "GET",
  headers: myHeaders,
  redirect: "follow",
};

fetch(
  "https://backend.leadconnectorhq.com/locations/hx0dWi6WjqUWvzY1wMjP/customFields/search?parentId=ZUF7FT5VzvOdpv5ne1TQ&skip=0&limit=5&documentType=field&model=all&query=&includeStandards=true",
  requestOptions
)
  .then((response) => response.text())
  .then((result) => console.log(result))
  .catch((error) => console.error(error));
