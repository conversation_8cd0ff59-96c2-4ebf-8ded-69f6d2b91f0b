const myHeaders = new Headers();
myHeaders.append(
  "authorization",
  "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzb3VyY2UiOiJXRUJfVVNFUiIsImNoYW5uZWwiOiJBUFAiLCJzb3VyY2VJZCI6ImdNN09na0lUZ2ZmQVIxNXRVN2tWIiwic291cmNlTmFtZSI6IkFsLUFtaW4gSXNsYW1OZXJvYiAiLCJjb21wYW55SWQiOiJpRTd1NGJNcmhDcG4xVWl1Y0lqWCIsIm1ldGEiOnsidXNlclJvbGUiOiJhZG1pbiIsInVzZXJUeXBlIjoiYWdlbmN5In0sInByaW1hcnlVc2VyIjp7fSwiaWF0IjoxNzQ5MjE2MTY0LCJleHAiOjE3NDkyMTk3NjQsImp0aSI6IjY3ZjVjMWJjMDU4OGVlMTdhZDFlMGM0NiJ9.W3G8b6ZPEwt8aT4cColz3AVZ0BI-cOkIuU4lMKSvfxk"
);
myHeaders.append("channel", "APP");
myHeaders.append("source", "WEB_USER");
myHeaders.append(
  "token-id",
  "eyJhbGciOiJSUzI1NiIsImtpZCI6IjZlODk1YzQ3YTA0YzVmNmRlMzExMmFmZjE2ODFhMzUwNzdkMWNjZDQiLCJ0eXAiOiJKV1QifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bZrjCWCvgUCDvUQduZDIauFj0aCZuUIsquItMgg0Jhkq-N2AGyrQn8yf0j7jhma66NuNmFKUUEffgd79WnbNtqyK1RxMM5w3obQLl6d-NOKg4V1DLFfl28mEpNc_KYXcsn8RgDToxdEbmvoaupuoE5QNSbo2m7b_ob2-lrOXWoaORI4XS5TSM5qe8vw8gjRCd8D3M9Eilbl6gBJ-qGOnm7XWOyzK3dgHYagOdHq8BY3Sy2JOxX3pYGME3eglcShtd4iLqCJhkkw7K4ts4_zaNvob8UUwsSoLLk7qVhNXlwQuIe89FAtNEoqXNi1CTfEyxnRG03Bvb1862IoCh04IgQ"
);
myHeaders.append("version", "2021-07-28");

const requestOptions = {
  method: "GET",
  headers: myHeaders,
  redirect: "follow",
};

fetch(
  "https://backend.leadconnectorhq.com/locations/hx0dWi6WjqUWvzY1wMjP/customFields/search?parentId=ZUF7FT5VzvOdpv5ne1TQ&skip=0&limit=1000&documentType=field&model=all&query=&includeStandards=true",
  requestOptions
)
  .then((response) => response.text())
  .then((result) => console.log(result))
  .catch((error) => console.error(error));
