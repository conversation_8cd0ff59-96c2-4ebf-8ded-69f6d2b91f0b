fetch(
  "https://backend.leadconnectorhq.com/locations/hx0dWi6WjqUWvzY1wMjP/customFields/TFXDnS8UsIZLY3hGZLtq",
  {
    headers: {
      accept: "application/json, text/plain, */*",
      "accept-language": "en-US,en;q=0.9",
      authorization:
        "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzb3VyY2UiOiJXRUJfVVNFUiIsImNoYW5uZWwiOiJBUFAiLCJzb3VyY2VJZCI6ImdNN09na0lUZ2ZmQVIxNXRVN2tWIiwic291cmNlTmFtZSI6IkFsLUFtaW4gSXNsYW1OZXJvYiAiLCJjb21wYW55SWQiOiJpRTd1NGJNcmhDcG4xVWl1Y0lqWCIsIm1ldGEiOnsidXNlclJvbGUiOiJhZG1pbiIsInVzZXJUeXBlIjoiYWdlbmN5In0sInByaW1hcnlVc2VyIjp7fSwiaWF0IjoxNzQ5MjE2MTY0LCJleHAiOjE3NDkyMTk3NjQsImp0aSI6IjY3ZjVjMWJjMDU4OGVlMTdhZDFlMGM0NiJ9.W3G8b6ZPEwt8aT4cColz3AVZ0BI-cOkIuU4lMKSvfxk",
      baggage:
        "sentry-environment=production,sentry-release=6d6335b2963287e57e420b0b4f6e62aa7859c7f7,sentry-public_key=c67431ff70d6440fb529c2705792425f,sentry-trace_id=d3a7a8ab28a24d0ebe6e7cc5416edeb8",
      channel: "APP",
      "content-type": "application/json",
      priority: "u=1, i",
      "sec-ch-ua":
        '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
      "sec-ch-ua-mobile": "?0",
      "sec-ch-ua-platform": '"macOS"',
      "sec-fetch-dest": "empty",
      "sec-fetch-mode": "cors",
      "sec-fetch-site": "cross-site",
      "sentry-trace": "d3a7a8ab28a24d0ebe6e7cc5416edeb8-a6a008e67abe2272",
      source: "WEB_USER",
      "token-id":
        "eyJhbGciOiJSUzI1NiIsImtpZCI6IjZlODk1YzQ3YTA0YzVmNmRlMzExMmFmZjE2ODFhMzUwNzdkMWNjZDQiLCJ0eXAiOiJKV1QifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bZrjCWCvgUCDvUQduZDIauFj0aCZuUIsquItMgg0Jhkq-N2AGyrQn8yf0j7jhma66NuNmFKUUEffgd79WnbNtqyK1RxMM5w3obQLl6d-NOKg4V1DLFfl28mEpNc_KYXcsn8RgDToxdEbmvoaupuoE5QNSbo2m7b_ob2-lrOXWoaORI4XS5TSM5qe8vw8gjRCd8D3M9Eilbl6gBJ-qGOnm7XWOyzK3dgHYagOdHq8BY3Sy2JOxX3pYGME3eglcShtd4iLqCJhkkw7K4ts4_zaNvob8UUwsSoLLk7qVhNXlwQuIe89FAtNEoqXNi1CTfEyxnRG03Bvb1862IoCh04IgQ",
    },
    referrer: "https://app.autopatient.co/",
    referrerPolicy: "strict-origin-when-cross-origin",
    body: "{}",
    method: "DELETE",
    mode: "cors",
    credentials: "include",
  }
);
