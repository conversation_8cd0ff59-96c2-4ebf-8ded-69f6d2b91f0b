#!/usr/bin/env node

import inquirer from "inquirer";
import fs from "fs-extra";
import path from "path";
import { CustomFieldsWorkflow } from "./main.js";
import { CustomFieldsManager, Logger } from "./customFieldsManager.js";

/**
 * Enhanced Interactive Command Line Interface for Custom Fields Management
 */
class EnhancedCustomFieldsCLI {
  constructor() {
    this.workflow = new CustomFieldsWorkflow();
    this.manager = new CustomFieldsManager();
    this.allFields = [];
    this.backupDir = null;
  }

  /**
   * Display welcome message and main menu
   */
  displayWelcome() {
    console.log(`
╔══════════════════════════════════════════════════════════════╗
║                🔧 Custom Fields Management CLI                ║
║                     Enhanced Interactive Mode                 ║
╚══════════════════════════════════════════════════════════════╝

Welcome to the enhanced Custom Fields Management CLI!
This tool will guide you through safely managing your custom fields.

Features:
✅ Interactive pagination configuration
✅ Automatic backup management
✅ Smart field selection
✅ Mandatory safety backups
✅ Progress indicators and confirmations
`);
  }

  /**
   * Display help information
   */
  displayHelp() {
    console.log(`
🔧 Enhanced Custom Fields Management CLI

Usage: node src/cli.js [command]

Commands:
  interactive              Start interactive mode (recommended)
  backup                   Create backup of all custom fields
  list                     List all custom fields with interactive options
  delete                   Interactive deletion with field selection
  help                     Show this help message

Interactive Features:
• Guided pagination configuration
• Smart backup directory management
• Interactive field selection by number, name, or pattern
• Mandatory backup before deletion
• Real-time progress indicators
• Detailed confirmations

Examples:
  node src/cli.js interactive    # Start interactive mode
  node src/cli.js backup         # Quick backup
  node src/cli.js list           # Interactive field listing
  node src/cli.js delete         # Interactive deletion

🛡️  Safety Features:
• Automatic backups before all operations
• Dry-run confirmations
• Field selection validation
• Progress tracking
`);
  }

  /**
   * Create intelligent backup directory with timestamp
   */
  async createBackupDirectory() {
    const timestamp = new Date()
      .toISOString()
      .replace(/[:.]/g, "-")
      .split("T")[0];
    const defaultDir = `./backups/custom-fields-${timestamp}`;

    const { backupDir } = await inquirer.prompt([
      {
        type: "input",
        name: "backupDir",
        message: "📁 Backup directory:",
        default: defaultDir,
        validate: (input) => {
          if (!input.trim()) return "Backup directory cannot be empty";
          return true;
        },
      },
    ]);

    // Ensure directory exists
    await fs.ensureDir(backupDir);
    this.backupDir = backupDir;

    Logger.success(`✅ Backup directory ready: ${backupDir}`);
    return backupDir;
  }

  /**
   * Interactive pagination configuration
   */
  async configurePagination() {
    console.log("\n📊 Pagination Configuration");
    console.log("Configure how many custom fields to retrieve:");

    const { maxPages, pageSize, estimateTime } = await inquirer.prompt([
      {
        type: "number",
        name: "maxPages",
        message: "Maximum number of pages to retrieve:",
        default: 1,
        validate: (input) => {
          if (input < 1) return "Must retrieve at least 1 page";
          if (input > 100) return "Maximum 100 pages allowed for safety";
          return true;
        },
      },
      {
        type: "number",
        name: "pageSize",
        message: "Number of fields per page:",
        default: 1,
        validate: (input) => {
          if (input < 1) return "Minimum 1 fields per page";
          if (input > 1000)
            return "Maximum 1000 fields per page for API limits";
          return true;
        },
      },
      {
        type: "confirm",
        name: "estimateTime",
        message: "Show time estimation before starting?",
        default: true,
      },
    ]);

    const estimatedRecords = maxPages * pageSize;
    const estimatedTimeSeconds = Math.ceil(maxPages * 0.5 + 2); // Rough estimate

    if (estimateTime) {
      console.log(`\n📈 Estimation:`);
      console.log(`   Maximum records: ${estimatedRecords.toLocaleString()}`);
      console.log(`   Estimated time: ~${estimatedTimeSeconds} seconds`);
      console.log(`   Pages to fetch: ${maxPages}`);

      const { proceed } = await inquirer.prompt([
        {
          type: "confirm",
          name: "proceed",
          message: "Proceed with these settings?",
          default: true,
        },
      ]);

      if (!proceed) {
        Logger.info("Operation cancelled by user");
        return null;
      }
    }

    return { maxPages, pageSize };
  }

  /**
   * Enhanced field retrieval with progress tracking
   */
  async retrieveFieldsWithProgress(paginationConfig = null) {
    let config = paginationConfig;

    if (!config) {
      config = await this.configurePagination();
      if (!config) return [];
    }

    Logger.info("🔄 Starting enhanced field retrieval...");

    // Override the manager's method to respect pagination limits
    const originalMethod = this.manager.getAllCustomFields;
    this.manager.getAllCustomFields = async (pageSize = 1000) => {
      return originalMethod.call(this.manager, pageSize, config.maxPages);
    };

    try {
      this.allFields = await this.manager.getAllCustomFields(config.pageSize);
      Logger.success(`✅ Retrieved ${this.allFields.length} custom fields`);
      return this.allFields;
    } catch (error) {
      Logger.error("Failed to retrieve fields", error);
      throw error;
    }
  }

  /**
   * Display fields in a readable table format
   */
  displayFieldsTable(fields, startIndex = 0, pageSize = 20) {
    if (!fields || fields.length === 0) {
      console.log("📭 No custom fields found.");
      return;
    }

    const endIndex = Math.min(startIndex + pageSize, fields.length);
    const pageFields = fields.slice(startIndex, endIndex);

    console.log(
      `\n📋 Custom Fields (${startIndex + 1}-${endIndex} of ${fields.length}):`
    );
    console.log(
      "┌─────┬─────────────────────────────────┬─────────────────┬──────────┬──────────┐"
    );
    console.log(
      "│ No. │ Name                            │ Type            │ Required │ ID       │"
    );
    console.log(
      "├─────┼─────────────────────────────────┼─────────────────┼──────────┼──────────┤"
    );

    pageFields.forEach((field, index) => {
      const num = (startIndex + index + 1).toString().padStart(3);
      const name = (field.name || field.label || "Unknown")
        .substring(0, 30)
        .padEnd(30);
      const type = (field.type || "Unknown").substring(0, 14).padEnd(14);
      const required = (field.required ? "Yes" : "No").padEnd(8);
      const id = (field.id || "").substring(0, 8).padEnd(8);

      console.log(`│ ${num} │ ${name} │ ${type} │ ${required} │ ${id} │`);
    });

    console.log(
      "└─────┴─────────────────────────────────┴─────────────────┴──────────┴──────────┘"
    );

    if (endIndex < fields.length) {
      console.log(
        `\n📄 Showing ${pageSize} of ${fields.length} fields. Use pagination to see more.`
      );
    }
  }

  /**
   * Interactive field selection with multiple methods
   */
  async selectFieldsInteractively(fields) {
    if (!fields || fields.length === 0) {
      Logger.warn("No fields available for selection");
      return [];
    }

    console.log("\n🎯 Field Selection Options");

    const { selectionMethod } = await inquirer.prompt([
      {
        type: "list",
        name: "selectionMethod",
        message: "How would you like to select fields?",
        choices: [
          { name: "🔢 Select by numbers (e.g., 1,3,5-10)", value: "numbers" },
          {
            name: '🔍 Select by name pattern (e.g., "test*", "*temp*")',
            value: "pattern",
          },
          { name: "📝 Select by field type", value: "type" },
          { name: "📋 Browse and select interactively", value: "browse" },
          { name: "❌ Cancel selection", value: "cancel" },
        ],
      },
    ]);

    if (selectionMethod === "cancel") {
      return [];
    }

    let selectedFields = [];

    switch (selectionMethod) {
      case "numbers":
        selectedFields = await this.selectByNumbers(fields);
        break;
      case "pattern":
        selectedFields = await this.selectByPattern(fields);
        break;
      case "type":
        selectedFields = await this.selectByType(fields);
        break;
      case "browse":
        selectedFields = await this.selectByBrowsing(fields);
        break;
    }

    if (selectedFields.length > 0) {
      console.log(
        `\n✅ Selected ${selectedFields.length} fields for deletion:`
      );
      selectedFields.forEach((field, index) => {
        console.log(
          `   ${index + 1}. ${field.name || field.label} (${field.id})`
        );
      });

      const { confirm } = await inquirer.prompt([
        {
          type: "confirm",
          name: "confirm",
          message: `Confirm selection of ${selectedFields.length} fields?`,
          default: false,
        },
      ]);

      if (!confirm) {
        Logger.info("Selection cancelled by user");
        return [];
      }
    }

    return selectedFields;
  }

  /**
   * Select fields by numbers (e.g., 1,3,5-10)
   */
  async selectByNumbers(fields) {
    this.displayFieldsTable(fields);

    const { numberInput } = await inquirer.prompt([
      {
        type: "input",
        name: "numberInput",
        message: "Enter field numbers (e.g., 1,3,5-10):",
        validate: (input) => {
          if (!input.trim()) return "Please enter at least one number";
          return true;
        },
      },
    ]);

    const selectedFields = [];
    const parts = numberInput.split(",");

    for (const part of parts) {
      const trimmed = part.trim();
      if (trimmed.includes("-")) {
        // Range selection (e.g., 5-10)
        const [start, end] = trimmed.split("-").map((n) => parseInt(n.trim()));
        if (start && end && start <= end) {
          for (let i = start; i <= end; i++) {
            if (i >= 1 && i <= fields.length) {
              selectedFields.push(fields[i - 1]);
            }
          }
        }
      } else {
        // Single number
        const num = parseInt(trimmed);
        if (num >= 1 && num <= fields.length) {
          selectedFields.push(fields[num - 1]);
        }
      }
    }

    return [...new Set(selectedFields)]; // Remove duplicates
  }

  /**
   * Select fields by name pattern
   */
  async selectByPattern(fields) {
    const { pattern } = await inquirer.prompt([
      {
        type: "input",
        name: "pattern",
        message:
          'Enter name pattern (use * for wildcards, e.g., "test*", "*temp*"):',
        validate: (input) => {
          if (!input.trim()) return "Please enter a pattern";
          return true;
        },
      },
    ]);

    const regex = new RegExp(pattern.replace(/\*/g, ".*"), "i");
    const matchedFields = fields.filter((field) => {
      const name = field.name || field.label || "";
      return regex.test(name);
    });

    if (matchedFields.length === 0) {
      Logger.warn(`No fields found matching pattern: ${pattern}`);
      return [];
    }

    console.log(
      `\n✅ Found ${matchedFields.length} fields matching pattern "${pattern}":`
    );
    this.displayFieldsTable(matchedFields);

    const { confirm } = await inquirer.prompt([
      {
        type: "confirm",
        name: "confirm",
        message: `Select all ${matchedFields.length} matching fields?`,
        default: false,
      },
    ]);

    return confirm ? matchedFields : [];
  }

  /**
   * Select fields by type
   */
  async selectByType(fields) {
    const fieldTypes = [
      ...new Set(fields.map((field) => field.type || "Unknown")),
    ];

    const { selectedTypes } = await inquirer.prompt([
      {
        type: "checkbox",
        name: "selectedTypes",
        message: "Select field types to delete:",
        choices: fieldTypes.map((type) => ({
          name: `${type} (${
            fields.filter((f) => (f.type || "Unknown") === type).length
          } fields)`,
          value: type,
        })),
      },
    ]);

    if (selectedTypes.length === 0) {
      return [];
    }

    const matchedFields = fields.filter((field) =>
      selectedTypes.includes(field.type || "Unknown")
    );

    console.log(`\n✅ Found ${matchedFields.length} fields of selected types:`);
    this.displayFieldsTable(matchedFields);

    const { confirm } = await inquirer.prompt([
      {
        type: "confirm",
        name: "confirm",
        message: `Select all ${matchedFields.length} fields of these types?`,
        default: false,
      },
    ]);

    return confirm ? matchedFields : [];
  }

  /**
   * Select fields by browsing with pagination
   */
  async selectByBrowsing(fields) {
    const selectedFields = [];
    let currentPage = 0;
    const pageSize = 20;
    let done = false;

    while (!done) {
      this.displayFieldsTable(fields, currentPage * pageSize, pageSize);

      const choices = [
        { name: "➕ Select fields from this page", value: "select" },
        {
          name: "📄 Next page",
          value: "next",
          disabled: (currentPage + 1) * pageSize >= fields.length,
        },
        {
          name: "📄 Previous page",
          value: "prev",
          disabled: currentPage === 0,
        },
        { name: "✅ Finish selection", value: "done" },
        { name: "❌ Cancel", value: "cancel" },
      ];

      const { action } = await inquirer.prompt([
        {
          type: "list",
          name: "action",
          message: `Page ${currentPage + 1} of ${Math.ceil(
            fields.length / pageSize
          )} | Selected: ${selectedFields.length}`,
          choices,
        },
      ]);

      switch (action) {
        case "select":
          const pageFields = fields.slice(
            currentPage * pageSize,
            (currentPage + 1) * pageSize
          );
          const { selectedNumbers } = await inquirer.prompt([
            {
              type: "checkbox",
              name: "selectedNumbers",
              message: "Select fields from this page:",
              choices: pageFields.map((field, index) => ({
                name: `${currentPage * pageSize + index + 1}. ${
                  field.name || field.label
                } (${field.type})`,
                value: field,
              })),
            },
          ]);
          selectedFields.push(...selectedNumbers);
          break;
        case "next":
          currentPage++;
          break;
        case "prev":
          currentPage--;
          break;
        case "done":
          done = true;
          break;
        case "cancel":
          return [];
      }
    }

    return [...new Set(selectedFields)]; // Remove duplicates
  }

  /**
   * Main interactive mode
   */
  async runInteractiveMode() {
    this.displayWelcome();

    const { action } = await inquirer.prompt([
      {
        type: "list",
        name: "action",
        message: "What would you like to do?",
        choices: [
          { name: "📦 Create backup of all custom fields", value: "backup" },
          { name: "📋 List and browse custom fields", value: "list" },
          {
            name: "🗑️  Delete custom fields (interactive selection)",
            value: "delete",
          },
          { name: "🧪 Dry run deletion (test mode)", value: "dry-run" },
          { name: "❓ Show help", value: "help" },
          { name: "🚪 Exit", value: "exit" },
        ],
      },
    ]);

    switch (action) {
      case "backup":
        await this.interactiveBackup();
        break;
      case "list":
        await this.interactiveList();
        break;
      case "delete":
        await this.interactiveDeletion(false);
        break;
      case "dry-run":
        await this.interactiveDeletion(true);
        break;
      case "help":
        this.displayHelp();
        break;
      case "exit":
        Logger.info("👋 Goodbye!");
        return;
    }
  }

  /**
   * Interactive backup process
   */
  async interactiveBackup() {
    Logger.info("🚀 Starting interactive backup process...");

    // Configure pagination
    const paginationConfig = await this.configurePagination();
    if (!paginationConfig) return;

    // Setup backup directory
    const backupDir = await this.createBackupDirectory();

    // Retrieve fields
    const fields = await this.retrieveFieldsWithProgress(paginationConfig);

    if (fields.length === 0) {
      Logger.warn("No fields found to backup");
      return;
    }

    // Create backup
    const backupPath = await this.manager.createBackup(fields, backupDir);

    Logger.success(`✅ Backup completed successfully!`);
    Logger.info(`📁 Backup saved to: ${backupPath}`);
    Logger.info(`📊 Total fields backed up: ${fields.length}`);
  }

  /**
   * Execute the specified command
   */
  /**
   * Interactive list and browse
   */
  async interactiveList() {
    Logger.info("🚀 Starting interactive field listing...");

    // Configure pagination
    const paginationConfig = await this.configurePagination();
    if (!paginationConfig) return;

    // Setup backup directory
    const backupDir = await this.createBackupDirectory();

    // Retrieve fields
    const fields = await this.retrieveFieldsWithProgress(paginationConfig);

    if (fields.length === 0) {
      Logger.warn("No fields found");
      return;
    }

    // Display fields with pagination
    let currentPage = 0;
    const pageSize = 20;
    let done = false;

    while (!done) {
      this.displayFieldsTable(fields, currentPage * pageSize, pageSize);

      const choices = [
        {
          name: "📄 Next page",
          value: "next",
          disabled: (currentPage + 1) * pageSize >= fields.length,
        },
        {
          name: "📄 Previous page",
          value: "prev",
          disabled: currentPage === 0,
        },
        { name: "💾 Create backup", value: "backup" },
        { name: "🔍 Search fields", value: "search" },
        { name: "✅ Done", value: "done" },
      ];

      const { action } = await inquirer.prompt([
        {
          type: "list",
          name: "action",
          message: `Page ${currentPage + 1} of ${Math.ceil(
            fields.length / pageSize
          )} | Total: ${fields.length} fields`,
          choices,
        },
      ]);

      switch (action) {
        case "next":
          currentPage++;
          break;
        case "prev":
          currentPage--;
          break;
        case "backup":
          const backupPath = await this.manager.createBackup(fields, backupDir);
          Logger.success(`✅ Backup created: ${backupPath}`);
          break;
        case "search":
          await this.searchFields(fields);
          break;
        case "done":
          done = true;
          break;
      }
    }
  }

  /**
   * Interactive deletion process
   */
  async interactiveDeletion(dryRun = false) {
    const mode = dryRun ? "DRY RUN" : "DELETION";
    Logger.info(`🚀 Starting interactive ${mode.toLowerCase()} process...`);

    // Configure pagination
    const paginationConfig = await this.configurePagination();
    if (!paginationConfig) return;

    // Setup backup directory (mandatory)
    const backupDir = await this.createBackupDirectory();

    // Retrieve fields
    const fields = await this.retrieveFieldsWithProgress(paginationConfig);

    if (fields.length === 0) {
      Logger.warn("No fields found");
      return;
    }

    // MANDATORY backup before deletion
    Logger.info("💾 Creating mandatory backup before deletion...");
    const backupPath = await this.manager.createBackup(fields, backupDir);
    Logger.success(`✅ Backup created: ${backupPath}`);

    // Interactive field selection
    const selectedFields = await this.selectFieldsInteractively(fields);

    if (selectedFields.length === 0) {
      Logger.info("No fields selected for deletion");
      return;
    }

    // Final confirmation
    console.log(`\n⚠️  ${mode} CONFIRMATION`);
    console.log(
      `You are about to ${dryRun ? "simulate deletion of" : "DELETE"} ${
        selectedFields.length
      } custom fields:`
    );

    selectedFields.forEach((field, index) => {
      console.log(
        `   ${index + 1}. ${field.name || field.label} (ID: ${field.id})`
      );
    });

    console.log(`\n📁 Backup location: ${backupPath}`);

    const { finalConfirm } = await inquirer.prompt([
      {
        type: "confirm",
        name: "finalConfirm",
        message: `${
          dryRun
            ? "Proceed with DRY RUN?"
            : "⚠️  PROCEED WITH ACTUAL DELETION? This cannot be undone!"
        }`,
        default: false,
      },
    ]);

    if (!finalConfirm) {
      Logger.info("Operation cancelled by user");
      return;
    }

    // Execute deletion
    const fieldIds = selectedFields.map((field) => field.id);
    const result = await this.manager.deleteMultipleCustomFields(fieldIds, {
      dryRun,
      requireConfirmation: false, // We already confirmed
      batchSize: 5,
      delayBetweenBatches: 1000,
    });

    // Display results
    console.log(`\n🎯 ${mode} Results:`);
    console.log(`✅ Successful: ${result.successful.length}`);
    console.log(`❌ Failed: ${result.failed.length}`);
    console.log(`📊 Total: ${result.total}`);

    if (result.failed.length > 0) {
      console.log("\n❌ Failed deletions:");
      result.failed.forEach((failure) => {
        console.log(`   - ${failure.fieldId}: ${failure.error}`);
      });
    }
  }

  /**
   * Search fields functionality
   */
  async searchFields(fields) {
    const { searchTerm } = await inquirer.prompt([
      {
        type: "input",
        name: "searchTerm",
        message: "Enter search term (searches name, type, ID):",
        validate: (input) => {
          if (!input.trim()) return "Please enter a search term";
          return true;
        },
      },
    ]);

    const searchRegex = new RegExp(searchTerm, "i");
    const matchedFields = fields.filter((field) => {
      const name = field.name || field.label || "";
      const type = field.type || "";
      const id = field.id || "";
      return (
        searchRegex.test(name) || searchRegex.test(type) || searchRegex.test(id)
      );
    });

    if (matchedFields.length === 0) {
      Logger.warn(`No fields found matching: ${searchTerm}`);
    } else {
      console.log(
        `\n🔍 Found ${matchedFields.length} fields matching "${searchTerm}":`
      );
      this.displayFieldsTable(matchedFields);
    }
  }

  async executeCommand(command, options = {}) {
    try {
      switch (command) {
        case "interactive":
        case undefined:
          await this.runInteractiveMode();
          break;
        case "backup":
          await this.interactiveBackup();
          break;
        case "list":
          await this.interactiveList();
          break;
        case "delete":
          await this.interactiveDeletion(false);
          break;
        case "dry-run":
          await this.interactiveDeletion(true);
          break;
        case "help":
        case "--help":
        case "-h":
          this.displayHelp();
          break;
        default:
          Logger.error(`❌ Unknown command: ${command}`);
          this.displayHelp();
          break;
      }
    } catch (error) {
      Logger.error("❌ Command execution failed", error);
      process.exit(1);
    }
  }

  /**
   * Main CLI entry point
   */
  async run() {
    const args = process.argv;
    const command = args[2];

    Logger.info("🚀 Enhanced Custom Fields Management CLI Started");

    // Default to interactive mode if no command specified
    await this.executeCommand(command || "interactive");

    Logger.info("✅ CLI execution completed");
  }
}

// Update the CustomFieldsManager to support max pages
const originalGetAllCustomFields =
  CustomFieldsManager.prototype.getAllCustomFields;
CustomFieldsManager.prototype.getAllCustomFields = async function (
  pageSize = 1000,
  maxPages = null
) {
  Logger.info("Starting custom fields retrieval with pagination...");

  let allFields = [];
  let skip = 0;
  let hasMoreData = true;
  let pageNumber = 1;

  try {
    while (hasMoreData && (!maxPages || pageNumber <= maxPages)) {
      Logger.info(
        `Fetching page ${pageNumber} (skip: ${skip}, limit: ${pageSize})`
      );

      const url = `${
        this.constructor.API_CONFIG?.baseUrl ||
        "https://backend.leadconnectorhq.com"
      }/locations/${
        this.constructor.API_CONFIG?.locationId || "hx0dWi6WjqUWvzY1wMjP"
      }/customFields/search?parentId=${
        this.constructor.API_CONFIG?.parentId || "ZUF7FT5VzvOdpv5ne1TQ"
      }&skip=${skip}&limit=${pageSize}&documentType=field&model=all&query=&includeStandards=true`;

      const response = await fetch(url, {
        method: "GET",
        headers: this.constructor.API_CONFIG?.headers || {},
        redirect: "follow",
      });

      if (!response.ok) {
        throw new Error(
          `HTTP error! status: ${response.status} - ${response.statusText}`
        );
      }

      const responseText = await response.text();
      let pageData;

      try {
        pageData = JSON.parse(responseText);
      } catch (parseError) {
        Logger.error("Failed to parse JSON response", parseError);
        throw new Error(`Invalid JSON response: ${parseError.message}`);
      }

      // Handle different response structures
      const fields = pageData.customFields || pageData.data || pageData;

      if (!Array.isArray(fields)) {
        Logger.warn("Unexpected response structure", pageData);
        break;
      }

      Logger.info(`Retrieved ${fields.length} fields on page ${pageNumber}`);
      allFields = allFields.concat(fields);

      // Check if we have more data
      hasMoreData = fields.length === pageSize;
      skip += pageSize;
      pageNumber++;

      // Add a small delay to be respectful to the API
      if (hasMoreData && (!maxPages || pageNumber <= maxPages)) {
        await new Promise((resolve) => setTimeout(resolve, 100));
      }
    }

    this.allCustomFields = allFields;
    this.totalRetrieved = allFields.length;

    Logger.success(
      `Successfully retrieved ${this.totalRetrieved} custom fields across ${
        pageNumber - 1
      } pages`
    );
    return allFields;
  } catch (error) {
    Logger.error("Failed to retrieve custom fields", error);
    throw error;
  }
};

// Run CLI if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const cli = new EnhancedCustomFieldsCLI();
  cli.run().catch((error) => {
    Logger.error("CLI execution failed", error);
    process.exit(1);
  });
}

export { EnhancedCustomFieldsCLI };
