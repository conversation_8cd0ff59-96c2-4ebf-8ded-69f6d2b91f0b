#!/usr/bin/env node

import { CustomFieldsWorkflow } from './main.js';
import { Logger } from './customFieldsManager.js';

/**
 * Command Line Interface for Custom Fields Management
 */
class CustomFieldsCLI {
  constructor() {
    this.workflow = new CustomFieldsWorkflow();
  }

  /**
   * Display help information
   */
  displayHelp() {
    console.log(`
🔧 Custom Fields Management CLI

Usage: node src/cli.js [command] [options]

Commands:
  backup                    Create backup of all custom fields
  list                     List all custom fields (with backup)
  delete <field-ids>       Delete specific custom fields (comma-separated)
  dry-run <field-ids>      Simulate deletion of specific custom fields
  help                     Show this help message

Options:
  --backup-dir <path>      Specify backup directory (default: ./backups)
  --no-backup             Skip backup creation (not recommended for delete operations)
  --batch-size <number>    Number of deletions per batch (default: 5)

Examples:
  node src/cli.js backup
  node src/cli.js list
  node src/cli.js dry-run field1,field2,field3
  node src/cli.js delete field1,field2 --backup-dir ./my-backups
  node src/cli.js delete field1 --no-backup

⚠️  WARNING: Deletion operations are permanent. Always create backups first!
`);
  }

  /**
   * Parse command line arguments
   */
  parseArgs(args) {
    const command = args[2];
    const options = {
      backupDir: './backups',
      createBackup: true,
      batchSize: 5,
      fieldsToDelete: []
    };

    // Parse field IDs if provided
    if (args[3] && !args[3].startsWith('--')) {
      options.fieldsToDelete = args[3].split(',').map(id => id.trim()).filter(id => id);
    }

    // Parse options
    for (let i = 2; i < args.length; i++) {
      const arg = args[i];
      
      if (arg === '--backup-dir' && args[i + 1]) {
        options.backupDir = args[i + 1];
        i++; // Skip next argument as it's the value
      } else if (arg === '--no-backup') {
        options.createBackup = false;
      } else if (arg === '--batch-size' && args[i + 1]) {
        options.batchSize = parseInt(args[i + 1], 10) || 5;
        i++; // Skip next argument as it's the value
      }
    }

    return { command, options };
  }

  /**
   * Execute the specified command
   */
  async executeCommand(command, options) {
    try {
      switch (command) {
        case 'backup':
          Logger.info('📦 Creating backup of all custom fields...');
          await this.workflow.backupOnly(options.backupDir);
          break;

        case 'list':
          Logger.info('📋 Listing all custom fields...');
          const listResult = await this.workflow.backupOnly(options.backupDir);
          Logger.info(`Found ${listResult.fieldsRetrieved} custom fields`);
          break;

        case 'dry-run':
          if (options.fieldsToDelete.length === 0) {
            Logger.error('❌ No field IDs provided for dry run');
            Logger.info('Usage: node src/cli.js dry-run field1,field2,field3');
            return;
          }
          Logger.info(`🧪 Dry run deletion for ${options.fieldsToDelete.length} fields...`);
          await this.workflow.dryRunDeletion(options.fieldsToDelete);
          break;

        case 'delete':
          if (options.fieldsToDelete.length === 0) {
            Logger.error('❌ No field IDs provided for deletion');
            Logger.info('Usage: node src/cli.js delete field1,field2,field3');
            return;
          }

          if (!options.createBackup) {
            Logger.warn('⚠️  WARNING: Proceeding without backup as requested!');
          }

          Logger.info(`🗑️  Deleting ${options.fieldsToDelete.length} custom fields...`);
          await this.workflow.executeWorkflow({
            createBackup: options.createBackup,
            fieldsToDelete: options.fieldsToDelete,
            dryRun: false,
            backupDir: options.backupDir
          });
          break;

        case 'help':
        case '--help':
        case '-h':
          this.displayHelp();
          break;

        default:
          if (!command) {
            Logger.info('ℹ️  No command specified. Showing help...');
            this.displayHelp();
          } else {
            Logger.error(`❌ Unknown command: ${command}`);
            this.displayHelp();
          }
          break;
      }
    } catch (error) {
      Logger.error('❌ Command execution failed', error);
      process.exit(1);
    }
  }

  /**
   * Main CLI entry point
   */
  async run() {
    const args = process.argv;
    const { command, options } = this.parseArgs(args);

    Logger.info('🚀 Custom Fields Management CLI Started');
    Logger.info(`Command: ${command || 'none'}`);
    
    if (options.fieldsToDelete.length > 0) {
      Logger.info(`Field IDs: ${options.fieldsToDelete.join(', ')}`);
    }

    await this.executeCommand(command, options);
    Logger.info('✅ CLI execution completed');
  }
}

// Interactive prompts utility (for future enhancement)
class InteractivePrompts {
  /**
   * Simulate user confirmation (in a real implementation, you'd use a library like 'inquirer')
   * @param {string} message - Confirmation message
   * @returns {Promise<boolean>} User's confirmation
   */
  static async confirm(message) {
    Logger.warn(`CONFIRMATION REQUIRED: ${message}`);
    Logger.info('In a real implementation, this would prompt for user input.');
    Logger.info('For now, assuming confirmation = true');
    return true; // In real implementation, get actual user input
  }

  /**
   * Get user input for field selection
   * @param {Array} fields - Available fields
   * @returns {Promise<Array>} Selected field IDs
   */
  static async selectFields(fields) {
    Logger.info('Available fields for selection:');
    fields.forEach((field, index) => {
      Logger.info(`${index + 1}. ${field.name} (ID: ${field.id})`);
    });
    
    Logger.info('In a real implementation, this would allow interactive field selection.');
    return []; // Return empty array for now
  }
}

// Run CLI if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const cli = new CustomFieldsCLI();
  cli.run().catch(error => {
    Logger.error('CLI execution failed', error);
    process.exit(1);
  });
}

export { CustomFieldsCLI, InteractivePrompts };
