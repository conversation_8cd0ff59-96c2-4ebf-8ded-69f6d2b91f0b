import { CustomFieldsManager, Logger } from "./customFieldsManager.js";

/**
 * Main workflow orchestrator for custom fields management
 */
class CustomFieldsWorkflow {
  constructor() {
    this.manager = new CustomFieldsManager();
    this.backupPath = null;
  }

  /**
   * Execute the complete workflow: retrieve, backup, and optionally delete
   * @param {Object} options - Workflow options
   * @param {boolean} options.createBackup - Whether to create backup (default: true)
   * @param {Array} options.fieldsToDelete - Array of field IDs to delete (optional)
   * @param {boolean} options.dryRun - If true, only simulate operations (default: false)
   * @param {string} options.backupDir - Directory for backups (default: './backups')
   */
  async executeWorkflow(options = {}) {
    const {
      createBackup = true,
      fieldsToDelete = [],
      dryRun = false,
      backupDir = "./backups",
    } = options;

    try {
      Logger.info("🚀 Starting Custom Fields Management Workflow");

      // Step 1: Retrieve all custom fields with pagination
      Logger.info("📥 Step 1: Retrieving all custom fields...");
      const allFields = await this.manager.getAllCustomFields();

      if (allFields.length === 0) {
        Logger.warn("No custom fields found. Workflow completed.");
        return { success: true, fieldsRetrieved: 0 };
      }

      // Step 2: Create backup if requested
      if (createBackup) {
        Logger.info("💾 Step 2: Creating backup...");
        this.backupPath = await this.manager.createBackup(allFields, backupDir);
      } else {
        Logger.info("⏭️  Step 2: Skipping backup (as requested)");
      }

      // Step 3: Display summary
      Logger.info("📊 Step 3: Custom Fields Summary");
      const summary = this.manager.getFieldsSummary();
      this.displaySummary(summary);

      // Step 4: Delete fields if specified
      let deletionResults = null;
      if (fieldsToDelete && fieldsToDelete.length > 0) {
        Logger.info(
          `🗑️  Step 4: Deleting ${fieldsToDelete.length} custom fields...`
        );

        if (!createBackup && !dryRun) {
          Logger.warn(
            "⚠️  WARNING: No backup was created and this is not a dry run!"
          );
          Logger.warn(
            "⚠️  Consider creating a backup before proceeding with deletions."
          );
        }

        deletionResults = await this.manager.deleteMultipleCustomFields(
          fieldsToDelete,
          {
            dryRun,
            requireConfirmation: true,
            batchSize: 5,
            delayBetweenBatches: 1000,
          }
        );
      } else {
        Logger.info("⏭️  Step 4: No fields specified for deletion");
      }

      // Step 5: Final summary
      Logger.info("✅ Workflow completed successfully!");
      const workflowResults = {
        success: true,
        fieldsRetrieved: allFields.length,
        backupCreated: createBackup,
        backupPath: this.backupPath,
        deletionResults,
      };

      this.displayWorkflowResults(workflowResults);
      return workflowResults;
    } catch (error) {
      Logger.error("❌ Workflow failed", error);
      throw error;
    }
  }

  /**
   * Display custom fields summary in a readable format
   * @param {Object} summary - Fields summary object
   */
  displaySummary(summary) {
    console.log("\n📋 Custom Fields Summary:");
    console.log(`Total Fields: ${summary.total}`);

    if (summary.fields && summary.fields.length > 0) {
      console.log("\nFields List:");
      summary.fields.forEach((field, index) => {
        console.log(
          `  ${index + 1}. ${field.name} (ID: ${field.id}, Type: ${field.type})`
        );
      });
    }
    console.log("");
  }

  /**
   * Display final workflow results
   * @param {Object} results - Workflow results object
   */
  displayWorkflowResults(results) {
    console.log("\n🎯 Workflow Results Summary:");
    console.log(`✅ Fields Retrieved: ${results.fieldsRetrieved}`);
    console.log(`💾 Backup Created: ${results.backupCreated ? "Yes" : "No"}`);

    if (results.backupPath) {
      console.log(`📁 Backup Location: ${results.backupPath}`);
    }

    if (results.deletionResults) {
      const { successful, failed, total, dryRun } = results.deletionResults;
      console.log(`🗑️  Deletion Results ${dryRun ? "(DRY RUN)" : ""}:`);
      console.log(`   Total Processed: ${total}`);
      console.log(`   Successful: ${successful.length}`);
      console.log(`   Failed: ${failed.length}`);
    }
    console.log("");
  }

  /**
   * Retrieve and backup only (no deletion)
   */
  async backupOnly(backupDir = "./backups") {
    return this.executeWorkflow({
      createBackup: true,
      fieldsToDelete: [],
      backupDir,
    });
  }

  /**
   * Dry run deletion (simulate deletion without actually deleting)
   * @param {Array} fieldsToDelete - Array of field IDs to simulate deletion
   */
  async dryRunDeletion(fieldsToDelete) {
    return this.executeWorkflow({
      createBackup: true,
      fieldsToDelete,
      dryRun: true,
    });
  }
}

// Example usage and CLI-like interface
async function main() {
  const workflow = new CustomFieldsWorkflow();

  try {
    // Example 1: Backup all custom fields
    Logger.info("Example 1: Creating backup of all custom fields...");
    await workflow.backupOnly();

    // Example 2: Dry run deletion (uncomment and modify as needed)
    // const fieldsToDelete = ['field-id-1', 'field-id-2']; // Replace with actual field IDs
    // Logger.info('Example 2: Dry run deletion...');
    // await workflow.dryRunDeletion(fieldsToDelete);

    // Example 3: Full workflow with actual deletion (uncomment and modify as needed)
    // Logger.info('Example 3: Full workflow with deletion...');
    // await workflow.executeWorkflow({
    //   createBackup: true,
    //   fieldsToDelete: ['field-id-to-delete'], // Replace with actual field IDs
    //   dryRun: false // Set to true for dry run
    // });
  } catch (error) {
    Logger.error("Main execution failed", error);
    process.exit(1);
  }
}

// Export for use in other modules
export { CustomFieldsWorkflow };

// Run main function if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
