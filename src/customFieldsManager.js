import fs from "fs-extra";
import path from "path";

// API Configuration extracted from existing files
const API_CONFIG = {
  baseUrl: "https://backend.leadconnectorhq.com",
  locationId: "hx0dWi6WjqUWvzY1wMjP",
  parentId: "ZUF7FT5VzvOdpv5ne1TQ",
  headers: {
    authorization:
      "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzb3VyY2UiOiJXRUJfVVNFUiIsImNoYW5uZWwiOiJBUFAiLCJzb3VyY2VJZCI6ImdNN09na0lUZ2ZmQVIxNXRVN2tWIiwic291cmNlTmFtZSI6IkFsLUFtaW4gSXNsYW1OZXJvYiAiLCJjb21wYW55SWQiOiJpRTd1NGJNcmhDcG4xVWl1Y0lqWCIsIm1ldGEiOnsidXNlclJvbGUiOiJhZG1pbiIsInVzZXJUeXBlIjoiYWdlbmN5In0sInByaW1hcnlVc2VyIjp7fSwiaWF0IjoxNzQ5MjE2MTY0LCJleHAiOjE3NDkyMTk3NjQsImp0aSI6IjY3ZjVjMWJjMDU4OGVlMTdhZDFlMGM0NiJ9.W3G8b6ZPEwt8aT4cColz3AVZ0BI-cOkIuU4lMKSvfxk",
    channel: "APP",
    source: "WEB_USER",
    "token-id":
      "eyJhbGciOiJSUzI1NiIsImtpZCI6IjZlODk1YzQ3YTA0YzVmNmRlMzExMmFmZjE2ODFhMzUwNzdkMWNjZDQiLCJ0eXAiOiJKV1QifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bZrjCWCvgUCDvUQduZDIauFj0aCZuUIsquItMgg0Jhkq-N2AGyrQn8yf0j7jhma66NuNmFKUUEffgd79WnbNtqyK1RxMM5w3obQLl6d-NOKg4V1DLFfl28mEpNc_KYXcsn8RgDToxdEbmvoaupuoE5QNSbo2m7b_ob2-lrOXWoaORI4XS5TSM5qe8vw8gjRCd8D3M9Eilbl6gBJ-qGOnm7XWOyzK3dgHYagOdHq8BY3Sy2JOxX3pYGME3eglcShtd4iLqCJhkkw7K4ts4_zaNvob8UUwsSoLLk7qVhNXlwQuIe89FAtNEoqXNi1CTfEyxnRG03Bvb1862IoCh04IgQ",
    version: "2021-07-28",
    accept: "application/json, text/plain, */*",
    "content-type": "application/json",
  },
};

/**
 * Utility function to create a delay
 */
const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

/**
 * Logger utility for consistent logging
 */
class Logger {
  static info(message, data = null) {
    console.log(`[INFO] ${new Date().toISOString()} - ${message}`);
    if (data) console.log(data);
  }

  static error(message, error = null) {
    console.error(`[ERROR] ${new Date().toISOString()} - ${message}`);
    if (error) console.error(error);
  }

  static warn(message, data = null) {
    console.warn(`[WARN] ${new Date().toISOString()} - ${message}`);
    if (data) console.warn(data);
  }

  static success(message, data = null) {
    console.log(`[SUCCESS] ${new Date().toISOString()} - ${message}`);
    if (data) console.log(data);
  }
}

/**
 * Custom Fields Manager Class
 * Handles retrieval, backup, and deletion of custom fields with pagination support
 */
export class CustomFieldsManager {
  constructor() {
    this.allCustomFields = [];
    this.totalRetrieved = 0;
  }

  /**
   * Retrieve all custom fields with pagination support
   * @param {number} pageSize - Number of records per page (default: 1000)
   * @returns {Promise<Array>} Array of all custom fields
   */
  async getAllCustomFields(pageSize = 1) {
    Logger.info("Starting custom fields retrieval with pagination...");

    let allFields = [];
    let skip = 0;
    let hasMoreData = true;
    let pageNumber = 1;

    try {
      while (hasMoreData) {
        Logger.info(
          `Fetching page ${pageNumber} (skip: ${skip}, limit: ${pageSize})`
        );

        const url = `${API_CONFIG.baseUrl}/locations/${API_CONFIG.locationId}/customFields/search?parentId=${API_CONFIG.parentId}&skip=${skip}&limit=${pageSize}&documentType=field&model=all&query=&includeStandards=true`;

        const response = await fetch(url, {
          method: "GET",
          headers: API_CONFIG.headers,
          redirect: "follow",
        });

        if (!response.ok) {
          throw new Error(
            `HTTP error! status: ${response.status} - ${response.statusText}`
          );
        }

        const responseText = await response.text();
        let pageData;

        try {
          pageData = JSON.parse(responseText);
        } catch (parseError) {
          Logger.error("Failed to parse JSON response", parseError);
          throw new Error(`Invalid JSON response: ${parseError.message}`);
        }

        // Handle different response structures
        const fields = pageData.customFields || pageData.data || pageData;

        if (!Array.isArray(fields)) {
          Logger.warn("Unexpected response structure", pageData);
          break;
        }

        Logger.info(`Retrieved ${fields.length} fields on page ${pageNumber}`);
        allFields = allFields.concat(fields);

        // Check if we have more data
        hasMoreData = fields.length === pageSize;
        skip += pageSize;
        pageNumber++;

        // Add a small delay to be respectful to the API
        if (hasMoreData) {
          await delay(100);
        }
      }

      this.allCustomFields = allFields;
      this.totalRetrieved = allFields.length;

      Logger.success(
        `Successfully retrieved ${this.totalRetrieved} custom fields across ${
          pageNumber - 1
        } pages`
      );
      return allFields;
    } catch (error) {
      Logger.error("Failed to retrieve custom fields", error);
      throw error;
    }
  }

  /**
   * Create a backup of custom fields data
   * @param {Array} customFields - Array of custom fields to backup
   * @param {string} backupDir - Directory to save backup (default: './backups')
   * @returns {Promise<string>} Path to the created backup file
   */
  async createBackup(customFields = null, backupDir = "./backups") {
    const fieldsToBackup = customFields || this.allCustomFields;

    if (!fieldsToBackup || fieldsToBackup.length === 0) {
      throw new Error(
        "No custom fields data to backup. Please retrieve fields first."
      );
    }

    try {
      // Ensure backup directory exists
      await fs.ensureDir(backupDir);

      // Create timestamp for filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
      const filename = `custom-fields-backup-${timestamp}.json`;
      const filePath = path.join(backupDir, filename);

      // Create backup data with metadata
      const backupData = {
        metadata: {
          timestamp: new Date().toISOString(),
          totalFields: fieldsToBackup.length,
          backupVersion: "1.0",
          locationId: API_CONFIG.locationId,
          parentId: API_CONFIG.parentId,
        },
        customFields: fieldsToBackup,
      };

      // Write backup file with proper formatting
      await fs.writeJson(filePath, backupData, { spaces: 2 });

      Logger.success(`Backup created successfully: ${filePath}`);
      Logger.info(`Backed up ${fieldsToBackup.length} custom fields`);

      return filePath;
    } catch (error) {
      Logger.error("Failed to create backup", error);
      throw error;
    }
  }

  /**
   * Delete a single custom field by ID
   * @param {string} fieldId - ID of the custom field to delete
   * @param {boolean} dryRun - If true, only simulate the deletion (default: false)
   * @returns {Promise<Object>} Deletion result
   */
  async deleteCustomField(fieldId, dryRun = false) {
    if (!fieldId) {
      throw new Error("Field ID is required for deletion");
    }

    if (dryRun) {
      Logger.info(`[DRY RUN] Would delete custom field: ${fieldId}`);
      return { success: true, dryRun: true, fieldId };
    }

    try {
      const url = `${API_CONFIG.baseUrl}/locations/${API_CONFIG.locationId}/customFields/${fieldId}`;

      const response = await fetch(url, {
        method: "DELETE",
        headers: {
          ...API_CONFIG.headers,
          accept: "application/json, text/plain, */*",
          "accept-language": "en-US,en;q=0.9",
          baggage:
            "sentry-environment=production,sentry-release=6d6335b2963287e57e420b0b4f6e62aa7859c7f7,sentry-public_key=c67431ff70d6440fb529c2705792425f,sentry-trace_id=d3a7a8ab28a24d0ebe6e7cc5416edeb8",
          priority: "u=1, i",
          "sec-ch-ua":
            '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
          "sec-ch-ua-mobile": "?0",
          "sec-ch-ua-platform": '"macOS"',
          "sec-fetch-dest": "empty",
          "sec-fetch-mode": "cors",
          "sec-fetch-site": "cross-site",
          "sentry-trace": "d3a7a8ab28a24d0ebe6e7cc5416edeb8-a6a008e67abe2272",
        },
        referrer: "https://app.autopatient.co/",
        referrerPolicy: "strict-origin-when-cross-origin",
        body: "{}",
        mode: "cors",
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error(
          `HTTP error! status: ${response.status} - ${response.statusText}`
        );
      }

      Logger.success(`Successfully deleted custom field: ${fieldId}`);
      return { success: true, fieldId, status: response.status };
    } catch (error) {
      Logger.error(`Failed to delete custom field ${fieldId}`, error);
      throw error;
    }
  }

  /**
   * Delete multiple custom fields with confirmation and safety measures
   * @param {Array} fieldIds - Array of field IDs to delete
   * @param {Object} options - Deletion options
   * @param {boolean} options.dryRun - If true, only simulate deletions
   * @param {boolean} options.requireConfirmation - If true, require user confirmation
   * @param {number} options.batchSize - Number of deletions per batch
   * @param {number} options.delayBetweenBatches - Delay in ms between batches
   * @returns {Promise<Object>} Batch deletion results
   */
  async deleteMultipleCustomFields(fieldIds, options = {}) {
    const {
      dryRun = false,
      requireConfirmation = true,
      batchSize = 5,
      delayBetweenBatches = 1000,
    } = options;

    if (!Array.isArray(fieldIds) || fieldIds.length === 0) {
      throw new Error("Field IDs array is required and must not be empty");
    }

    Logger.info(`Preparing to delete ${fieldIds.length} custom fields`);

    if (requireConfirmation && !dryRun) {
      Logger.warn("⚠️  WARNING: This will permanently delete custom fields!");
      Logger.info("Field IDs to delete:", fieldIds);
      // In a real implementation, you would add user input confirmation here
      // For now, we'll log the warning and proceed
    }

    const results = {
      total: fieldIds.length,
      successful: [],
      failed: [],
      dryRun,
    };

    try {
      // Process deletions in batches
      for (let i = 0; i < fieldIds.length; i += batchSize) {
        const batch = fieldIds.slice(i, i + batchSize);
        const batchNumber = Math.floor(i / batchSize) + 1;
        const totalBatches = Math.ceil(fieldIds.length / batchSize);

        Logger.info(
          `Processing batch ${batchNumber}/${totalBatches} (${batch.length} fields)`
        );

        // Process batch concurrently
        const batchPromises = batch.map(async (fieldId) => {
          try {
            const result = await this.deleteCustomField(fieldId, dryRun);
            results.successful.push({ fieldId, result });
            return { fieldId, success: true };
          } catch (error) {
            results.failed.push({ fieldId, error: error.message });
            return { fieldId, success: false, error };
          }
        });

        await Promise.all(batchPromises);

        // Add delay between batches (except for the last batch)
        if (i + batchSize < fieldIds.length) {
          Logger.info(`Waiting ${delayBetweenBatches}ms before next batch...`);
          await delay(delayBetweenBatches);
        }
      }

      Logger.success(
        `Batch deletion completed. Success: ${results.successful.length}, Failed: ${results.failed.length}`
      );

      if (results.failed.length > 0) {
        Logger.warn("Failed deletions:", results.failed);
      }

      return results;
    } catch (error) {
      Logger.error("Batch deletion failed", error);
      throw error;
    }
  }

  /**
   * Get custom fields summary for reporting
   * @returns {Object} Summary of custom fields
   */
  getFieldsSummary() {
    if (!this.allCustomFields || this.allCustomFields.length === 0) {
      return { total: 0, message: "No custom fields loaded" };
    }

    const summary = {
      total: this.allCustomFields.length,
      fields: this.allCustomFields.map((field) => ({
        id: field.id,
        name: field.name || field.label || "Unknown",
        type: field.type || "Unknown",
        required: field.required || false,
      })),
    };

    return summary;
  }
}

export { Logger, API_CONFIG };
